#!/usr/bin/env node

/**
 * API连接测试脚本
 * 用于测试小梅花API接口的连接性
 */

const axios = require('axios');
const https = require('https');

// 测试配置
const TEST_CONFIG = {
  timeout: 10000,
  retries: 3,
  testKey: 'test123'
};

// 创建HTTPS Agent，允许自签名证书
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

// 测试URL列表
const TEST_URLS = [
  'https://xiaomeihuakefu.cn',
  'https://xiaomeihuakefu.cn/api/verify.php',
  'https://api.xiaomeihuakefu.cn',
  'https://api.xiaomeihuakefu.cn/api/verify.php'
];

/**
 * 测试单个URL的连接性
 */
async function testUrl(url, isApiEndpoint = false) {
  console.log(`\n🔍 测试: ${url}`);
  
  try {
    const startTime = Date.now();
    let response;
    
    if (isApiEndpoint) {
      // 对于API端点，发送POST请求
      const postData = new URLSearchParams();
      postData.append('key', TEST_CONFIG.testKey);
      
      response = await axios.post(url, postData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'XiaoMeiHua-Test/1.0.0'
        },
        timeout: TEST_CONFIG.timeout,
        httpsAgent: httpsAgent,
        validateStatus: function (status) {
          return status >= 200 && status < 500; // 接受200-499状态码
        }
      });
    } else {
      // 对于普通URL，发送GET请求
      response = await axios.get(url, {
        timeout: TEST_CONFIG.timeout,
        httpsAgent: httpsAgent,
        validateStatus: function (status) {
          return status >= 200 && status < 500;
        }
      });
    }
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log(`✅ 成功: 状态码 ${response.status}, 响应时间 ${responseTime}ms`);
    
    if (isApiEndpoint && response.data) {
      console.log(`📄 响应内容: ${JSON.stringify(response.data)}`);
    }
    
    return {
      success: true,
      status: response.status,
      responseTime: responseTime,
      data: response.data
    };
    
  } catch (error) {
    console.log(`❌ 失败: ${error.message}`);
    console.log(`🔍 错误代码: ${error.code || 'N/A'}`);
    
    if (error.response) {
      console.log(`📊 HTTP状态: ${error.response.status}`);
      console.log(`📄 响应数据: ${JSON.stringify(error.response.data)}`);
    }
    
    return {
      success: false,
      error: error.message,
      code: error.code,
      status: error.response?.status
    };
  }
}

/**
 * 执行完整的连接测试
 */
async function runConnectionTest() {
  console.log('🚀 开始API连接测试...\n');
  console.log('=' * 50);
  
  const results = {};
  
  for (const url of TEST_URLS) {
    const isApiEndpoint = url.includes('/api/verify.php');
    const result = await testUrl(url, isApiEndpoint);
    results[url] = result;
    
    // 在测试之间添加短暂延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '=' * 50);
  console.log('📊 测试结果汇总:');
  console.log('=' * 50);
  
  let successCount = 0;
  let totalCount = 0;
  
  for (const [url, result] of Object.entries(results)) {
    totalCount++;
    const domain = new URL(url).hostname;
    const path = new URL(url).pathname;
    
    if (result.success) {
      successCount++;
      console.log(`✅ ${domain}${path}: 正常 (${result.responseTime}ms)`);
    } else {
      console.log(`❌ ${domain}${path}: ${result.error}`);
    }
  }
  
  console.log('\n' + '=' * 50);
  console.log(`📈 成功率: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
  
  if (successCount === 0) {
    console.log('🚨 所有连接都失败了！可能的原因：');
    console.log('   1. 网络连接问题');
    console.log('   2. DNS解析问题');
    console.log('   3. 防火墙阻止');
    console.log('   4. 服务器维护中');
  } else if (successCount < totalCount) {
    console.log('⚠️  部分连接失败，请检查具体的失败原因');
  } else {
    console.log('🎉 所有连接测试通过！');
  }
  
  return results;
}

// 如果直接运行此脚本
if (require.main === module) {
  runConnectionTest()
    .then(() => {
      console.log('\n✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { runConnectionTest, testUrl };
