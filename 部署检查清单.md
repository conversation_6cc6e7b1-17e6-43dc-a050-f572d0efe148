# 小梅花API部署检查清单

## 问题诊断
✅ **问题确认**：API接口返回404错误，说明服务器上缺少API文件

## 必须部署的文件和目录

### 1. API核心文件
```
网站后台/api/ → 服务器/api/
├── verify.php          # 卡密验证接口（核心）
├── gateway.php         # API网关
├── heartbeat.php       # 心跳检测
├── popup.php           # 弹窗API
├── agreement.php       # 协议API
├── script.php          # 脚本API
└── 其他API文件...
```

### 2. 依赖文件
```
网站后台/includes/ → 服务器/includes/
├── db.php              # 数据库连接
├── functions.php       # 公共函数
├── license_middleware.php  # 卡密验证中间件
└── security_monitor.php    # 安全监控
```

### 3. 配置文件
```
网站后台/config/ → 服务器/config/
└── 数据库配置文件
```

## 部署步骤

### 步骤1：上传文件
1. 使用FTP/SFTP工具连接到您的服务器
2. 将以下目录上传到网站根目录：
   - `网站后台/api/` → `服务器根目录/api/`
   - `网站后台/includes/` → `服务器根目录/includes/`
   - `网站后台/config/` → `服务器根目录/config/`

### 步骤2：设置文件权限
```bash
chmod 755 api/
chmod 644 api/*.php
chmod 755 includes/
chmod 644 includes/*.php
```

### 步骤3：配置数据库
1. 检查 `includes/db.php` 中的数据库连接配置
2. 确保数据库服务器、用户名、密码、数据库名正确
3. 运行数据库初始化脚本（如果需要）

### 步骤4：测试API接口
```bash
# 测试基本连接
curl -X POST "https://xiaomeihuakefu.cn/api/verify.php" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "key=test123"

# 期望返回：{"success":false,"message":"卡密不能为空"} 或类似的JSON响应
# 而不是404错误
```

## 验证部署成功

### 1. API接口测试
- ✅ `https://xiaomeihuakefu.cn/api/verify.php` 返回JSON而不是404
- ✅ 使用有效卡密能够正常验证
- ✅ 使用无效卡密返回相应错误信息

### 2. APP测试
- ✅ APP能够正常连接到API
- ✅ 卡密验证功能正常工作
- ✅ 不再显示"网络错误，请检查网络连接"

## 常见问题排查

### 问题1：仍然返回404
**原因**：文件路径不正确
**解决**：确保API文件在正确的路径下，检查服务器目录结构

### 问题2：返回500错误
**原因**：PHP语法错误或数据库连接问题
**解决**：检查服务器错误日志，修复PHP错误或数据库配置

### 问题3：返回数据库连接错误
**原因**：数据库配置不正确
**解决**：检查 `includes/db.php` 中的数据库连接参数

### 问题4：权限错误
**原因**：文件权限设置不正确
**解决**：设置正确的文件权限（644用于PHP文件，755用于目录）

## 部署后的APP修复

一旦API正确部署，APP中的网络错误问题应该会自动解决。如果仍有问题，可以：

1. 清除APP缓存
2. 重新启动APP
3. 使用APP内置的网络测试功能进行诊断

## 联系支持

如果按照以上步骤操作后仍有问题，请提供：
1. 服务器错误日志
2. API测试结果
3. APP错误信息
4. 服务器环境信息（PHP版本、数据库版本等）
